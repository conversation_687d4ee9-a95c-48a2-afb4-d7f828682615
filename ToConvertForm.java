package mdware.seisenedi.edi.exec_order.bean;

import java.util.List;

import jp.co.vinculumjapan.stc.util.servlet.DataHolder;
import mdware.seisenedi.common.util.StringUtility;
import mdware.seisenedi.common.util.SystemControlUtil;
import mdware.seisenedi.edi.exec_order.dictionary.ListTarget;
import mdware.seisenedi.edi.login.bean.UserBean;

/**
 * <p>タイトル: 納品一括処理画面ステータスクラス</p>
 * <p>説明: 納品一括処理画面の状態を保持するオブジェクトです。</p>
 * <p>著作権: Copyright (c) </p>
 * <p>会社名: Vincuram Japan corporation</p>
 * <AUTHOR>
 * @version 1.00 2024/11/01 VINX redmine＃29917_不要コメント削除
 * @version 1.01 2025/09/22 VINX redmine#XXXXX_UI改善対応
 */
public class NohinAllStatus {

	private List beanList = null;
	private int ROWS_IN_PAGE;
	///検索条件///
	private String torihikisaki_cd 	= null;
	private String torihikisaki_na 	= null;
	private String nohin_dt_from 		= null;
	private String nohin_dt_to 		= "";
	private String bunrui1_cd 			= "";
	private String bunrui1_na 			= "";
	private String bunrui2_cd 			= "";
	private String bunrui2_na			= "";
	private String bin_nm 				= "";
	private String tenpo_cd 			= "";
	private String tenpo_na 			= "";
	private String buturyu_kb 			= "";
	private String buturyu_nm 			= "";
	private String center_cd 			= "";
	private String center_na 			= "";	
	private boolean data_soba; 				//対象データ：相場
	private boolean data_hisoba_tokubai; 		//対象データ：非相場/特売
	private boolean data_spot; 				//対象データ：スポット
	private boolean data_kinkyu; 				//対象データ：緊急	
	private ListTarget list_target = null; 	// リスト出力対象
	
	/**
	 * コンストラクタ。
	 * @param userBean
	 */
	public NohinAllStatus() {
	}
	/**
	 * コンストラクタ。
	 * @param userBean
	 */
	public NohinAllStatus(UserBean userBean) {
		// 初期設定
		this.torihikisaki_cd 		= userBean.getTorihikisakiCd();
		this.torihikisaki_na 		= userBean.getTorihikisakiNa();
		this.nohin_dt_from 			= SystemControlUtil.getOnlineDate();		
		this.bunrui1_cd 			= userBean.getBunrui1Cd();
		this.bunrui1_na 			= userBean.getBunrui1Na();		
		this.list_target 			= ListTarget.ALL;
		this.center_cd 				= userBean.getCenterCd();
		this.center_na 				= userBean.getCenterNa();
		this.tenpo_cd 				= userBean.getTenpoCd();
		this.tenpo_na 				= userBean.getTenpoNa();	
		this.data_soba 				= true; // 対象データ　：相場		チェックオン
		this.data_hisoba_tokubai 	= true; // 対象データ　：非相場/特売チェックオン
		this.data_spot 				= true; // 対象データ　：スポット	チェックオン
		this.data_kinkyu 			= true; // 対象データ　：緊急		チェックオン
		this.ROWS_IN_PAGE 			= 0; 	// 表示行数
	}

	/**
	 * 納品年月日(from)と納品年月日(to)の前後関係をチェックし、矛盾があるならばfromとtoを入れ替えます。
	 */
	public void validNohinDtFromTo() {

		try {
			if ((this.nohin_dt_from.trim().length() <= 0 && this.nohin_dt_to.trim().length() > 0)
				|| Integer.parseInt(this.nohin_dt_from) > Integer.parseInt(this.nohin_dt_to)) {
				String temp = new String(this.nohin_dt_from);
				this.nohin_dt_from = new String(this.nohin_dt_to);
				this.nohin_dt_to = temp;
			}
		} catch (NumberFormatException nfe) {
		}
	}

	/**
	 * @return
	 */
	public String getBunrui1Cd() {
		return bunrui1_cd;
	}

	/**
	 * @return
	 */
	public String getBunrui1Na() {
		return bunrui1_na;
	}

	/**
	 * @return
	 */
	public String getBunrui2Cd() {
		return bunrui2_cd;
	}

	/**
	 * @return
	 */
	public String getBunrui2Na() {
		return bunrui2_na;
	}

	/**
	 * @return
	 */
	public String getNohinDtFrom() {
		return nohin_dt_from;
	}

	/**
	 * @return
	 */
	public String getNohinDtTo() {
		return nohin_dt_to;
	}

	/**
	 * @return
	 */
	public String getTenpoCd() {
		return tenpo_cd;
	}

	/**
	 * @return
	 */
	public String getTenpoNa() {
		return tenpo_na;
	}
	
	/**
	 * @return
	 */
	public String getCenterCd() {
		return center_cd;
	}

	/**
	 * @return
	 */
	public String getCenterNa() {
		return center_na;
	}	

	/**
	 * @return
	 */
	public String getButuryuKb() {
		return buturyu_kb;
	}

	/**
	 * @return
	 */
	public String getButuryuNm() {
		return buturyu_nm;
	}	
	/**
	 * @return
	 */
	public String getBinNm() {
		return bin_nm;
	}

	/**
	 * @return
	 */
	public String getTorihikisakiCd() {
		return torihikisaki_cd;
	}

	/**
	 * @return
	 */
	public String getTorihikisakiNa() {
		return torihikisaki_na;
	}

	/**
	 * @param string
	 */
	public void setBunrui1Cd(String string) {
		bunrui1_cd = string;
	}

	/**
	 * @param string
	 */
	public void setBunrui1Na(String string) {
		bunrui1_na = string;
	}

	/**
	 * @param string
	 */
	public void setBunrui2Cd(String string) {
		bunrui2_cd = string;
	}

	/**
	 * @param string
	 */
	public void setBunrui2Na(String string) {
		bunrui2_na = string;
	}

	/**
	 * @param string
	 */
	public void setNohinDtFrom(String string) {
		nohin_dt_from = string;
	}

	/**
	 * @param string
	 */
	public void setNohinDtTo(String string) {
		nohin_dt_to = string;
	}

	/**
	 * @param string
	 */
	//4桁まで頭"0"埋め
	public void setTenpoCd(String string) {
		if (string != null && string.trim().length() > 0) {
			tenpo_cd = StringUtility.adjustStringWithZero(string.trim(), 4);
		} else {
			tenpo_cd = string;
		}
	}

	/**
	 * @param string
	 */
	public void setTenpoNa(String string) {
		tenpo_na = string;
	}

	/**
	 * @param string
	 */
	//4桁まで頭"0"埋め
	public void setCenterCd(String string) {
		if (string != null && string.trim().length() > 0) {
			center_cd = StringUtility.adjustStringWithZero(string.trim(), 4);
		} else {
			center_cd = string;
		}
	}

	/**
	 * @param string
	 */
	public void setCenterNa(String string) {
		center_na = string;
	}
	
	/**
	 * @param string
	 */
	public void setButuryuKb(String string) {
		buturyu_kb = string;
	}
	
	/**
	 * @param string
	 */
	public void setButuryuNm(String string) {
		buturyu_nm = string;
	}
	
	/**
	 * @param string
	 */
	public void setBinNm(String string) {
		bin_nm = string;
	}

	/**
	 * @param string
	 */
	public void setTorihikisakiCd(String string) {
		torihikisaki_cd = string;
	}

	/**
	 * @param string
	 */
	public void setTorihikisakiNa(String string) {
		torihikisaki_na = string;
	}

	/**
	 * @return
	 */
	public ListTarget getListTarget() {
		return list_target;
	}

	/**
	 * @param target
	 */
	public void setListTarget(ListTarget target) {
		list_target = target;
	}

	/**
	 * data_sobaに対するsetterメソッド
	 * @param data_soba
	 */
	public void setDataSoba(boolean data_soba) {
		this.data_soba = data_soba;
	}
	/**
	 * data_sobaに対するisメソッド
	 * @return
	 */
	public boolean isDataSoba() {
		return this.data_soba;
	}

	/**
	 * data_hisoba_tokubaiに対するsetterメソッド
	 * @param data_hisoba_tokubai
	 */
	public void setDataHisobaTokubai(boolean data_hisoba_tokubai) {
		this.data_hisoba_tokubai = data_hisoba_tokubai;
	}
	/**
	 * data_hisoba_tokubaiに対するisメソッド
	 * @return
	 */
	public boolean isDataHisobaTokubai() {
		return this.data_hisoba_tokubai;
	}

	/**
	 * data_spotに対するsetterメソッド
	 * @param data_spot
	 */
	public void setDataSpot(boolean data_spot) {
		this.data_spot = data_spot;
	}
	/**
	 * data_spotに対するisメソッド
	 * @return
	 */
	public boolean isDataSpot() {
		return this.data_spot;
	}

	/**
	 * data_kinkyuに対するsetterメソッド
	 * @param data_soba
	 */
	public void setDataKinkyu(boolean data_kinkyu) {
		this.data_kinkyu = data_kinkyu;
	}
	/**
	 * data_kinkyuに対するisメソッド
	 * @return
	 */
	public boolean isDataKinkyu() {
		return this.data_kinkyu;
	}

	/**
	 * 検索条件の各項目をセットするメソッド
	 * @return
	 */
	public void setDataHolder(DataHolder dataHolder) {
		this.setTorihikisakiCd(dataHolder.getParameter("term_torihikisaki_cd"));	// 取引先コード
		this.setTorihikisakiNa(dataHolder.getParameter("term_torihikisaki_na"));	// 取引先名		
		this.setNohinDtFrom(dataHolder.getParameter("term_nohin_dt_from")); 		// 納品開始日
		this.setNohinDtTo(dataHolder.getParameter("term_nohin_dt_to")); 			// 納品終了日
		this.validNohinDtFromTo(); 											// 納品日From～Toチェック
		this.setBunrui1Cd(dataHolder.getParameter("term_bunrui1_cd")); 			// 大分類コード
		this.setBunrui1Na(dataHolder.getParameter("term_bunrui1_na"));			// 大分類名
		this.setBunrui2Cd(dataHolder.getParameter("term_bunrui2_cd")); 			// 中分類コード
		this.setBunrui2Na(dataHolder.getParameter("term_bunrui2_na"));			// 中分類名
		this.setTenpoCd(dataHolder.getParameter("term_tenpo_cd")); 				// 店舗コード
		this.setTenpoNa(dataHolder.getParameter("term_tenpo_na")); 				// 店舗名
		this.setCenterCd(dataHolder.getParameter("term_center_cd")); 			// センターコード
		this.setCenterNa(dataHolder.getParameter("term_center_na"));				// センター名
		this.setButuryuKb(dataHolder.getParameter("term_buturyu_kb")); 			// 物流区分
		this.setButuryuNm(dataHolder.getParameter("term_buturyu_na"));			// 物流区分名
		this.setBinNm(dataHolder.getParameter("term_bin_nm"));					// 便
		this.setDataSoba(dataHolder.getParameter("term_data_soba") != null); 	// 対象データ：相場
		this.setDataHisobaTokubai(dataHolder.getParameter("term_data_hisoba_tokubai") != null);
		// 対象データ：非相場/特売
		this.setDataSpot(dataHolder.getParameter("term_data_spot") != null); 	// 対象データ：スポット
		this.setDataKinkyu(dataHolder.getParameter("term_data_kinkyu") != null);// 対象データ：緊急
		this.setListTarget(ListTarget.ALL); 								// リスト出力対象：検索処理後はすべてのみ
	}
	
	/**
	 * @return
	 */
	public String toStringCondition() {
		StringBuffer sb = new StringBuffer();
		
		if(this.torihikisaki_cd != null && this.torihikisaki_cd.length() > 0){
			sb.append("  取引先コード : "		+ this.torihikisaki_cd + ", 取引先名 : "+ this.torihikisaki_na );	
		}
		if(this.nohin_dt_from != null && this.nohin_dt_from.length() > 0){
			sb.append(", 納品日(from) : "	+ this.nohin_dt_from );	
		}
		if(this.nohin_dt_to != null && this.nohin_dt_to.length() > 0){
			sb.append(", 納品日(to) : "		+ this.nohin_dt_to );	
		}
		if(this.bunrui1_cd != null && this.bunrui1_cd.length() > 0){
			sb.append(", 大分類コード : "		+ this.bunrui1_cd + ", 大分類名 : "		+ this.bunrui1_na);	
		}
		if(this.bunrui2_cd != null && this.bunrui2_cd.length() > 0){
			sb.append(", 中分類コード : "		+ this.bunrui2_cd + ", 中分類コード : "		+ this.bunrui2_cd);	
		}
		if(this.bin_nm != null && this.bin_nm.length() > 0){
			sb.append(", 便 : "		+ this.bin_nm + ", 便 : "	+ this.bin_nm);	
		}				
		if(this.tenpo_cd != null && this.tenpo_cd.length() > 0){
			sb.append(", 店舗コード : "			+ this.tenpo_cd		+ ", 店舗名 : "		+ this.tenpo_na);	
		}
		if(this.buturyu_kb != null && this.buturyu_kb.length() > 0){
			sb.append(", 物流区分 : " 			+ this.buturyu_kb  + ", 物流区分名 : "	+ this.buturyu_nm);	
		}
		if(this.center_cd != null && this.center_cd.length() > 0){
			sb.append(", センタコード : "		+ this.center_cd    + ", センタ名 : "		+ this.center_na);	
		}
		sb.append(", 対象データ : "	);
		if(this.isDataSoba()){
			sb.append("相場 "	);	
		}
		if(this.isDataHisobaTokubai()){
			sb.append("非相場/特売 "	);	
		}
		if(this.isDataKinkyu()){
			sb.append("緊急 "	);	
		}

		return sb.toString(); 
	}	

	/**１画面に表示する件数を返す。
	 * @return　ROWS_IN_PAGE
	 */
	public int getRowsInPage() {
		return this.ROWS_IN_PAGE;
	}

	/**
	 * @param list
	 */
	public void setBeanList(List list) {
		this.beanList = list;
	}

	/**
	 * @return
	 */
	public List getBeanList() {
		return beanList;
	}

	/**リストに存在するかどうかを判断する。
 	* @return　boolean
 	*/
	public boolean hasSearchResult() {
		return (beanList != null && beanList.size() > 0) ? true : false;
	}
}
