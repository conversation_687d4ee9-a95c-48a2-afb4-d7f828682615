package mdware.seisenedi.edi.exec_order.bean;

import mdware.seisenedi.common.util.StringUtility;
import mdware.seisenedi.common.util.SystemControlUtil;
import mdware.seisenedi.edi.exec_order.dictionary.HyojiKbDictionary;
import mdware.seisenedi.edi.exec_order.dictionary.ListTarget;
import mdware.seisenedi.edi.exec_order.dictionary.SyoriJokyoDictionary;
import mdware.seisenedi.edi.login.bean.UserBean;
/**
 * <p>タイトル: 納品予定～納品確定画面ステータスクラス</p>
 * <p>説明: 納品予定～納品確定画面の状態を保持するオブジェクトです。</p>
 * <p>著作権: Copyright (c) </p>
 * <p>会社名: Vincuram Japan corporation</p>
 * <AUTHOR>
 * @version 1.00 2024/11/01 VINX redmine＃29917_不要コメント削除
 * 
 */
public class NohinItiranStatus {

	public static final int ROWS_IN_PAGE = 30; 

	private String center_cd 		= "";
	private String center_na 		= "";
	private String nohin_dt 		= "";	
	private String torihikisaki_cd = null;
	private String torihikisaki_na = null;
	private String tenpo_cd 		= ""; 
	private String tenpo_na 		= "";
	private String bunrui1_cd 		= "";
	private String bunrui1_na 		= "";
	private String bunrui2_cd 		= "";
	private String bunrui2_na 		= "";
	private String bunrui5_cd 		= "";
	private String bunrui5_na 		= "";
	private String buturyu_kb 		= "";
	private String buturyu_nm 		= "";
	private String bin_kb 			= "";
	private SyoriJokyoDictionary syori_jokyo 		= null; // 処理状況
	private HyojiKbDictionary 	  hyoji_kb 			= null; // 表示形式
	private ListTarget 		  list_target 		= null; // リスト出力対象
	private String retrun_url_id 	= ""; // 取引先別作業状況確認画面より遷移した場合に利用

	private int torihikisakiCdLen;
	private int tenpoCdLen;
	private int bunrui1CdLen;
	private int bunrui2CdLen;
	private int bunrui5CdLen;

	/**
	 * コンストラクタ。
	 */
	public NohinItiranStatus() {
	}

	/**
	 * コンストラクタ。
	 * @param userBean
	 */
	public NohinItiranStatus(UserBean userBean) {

		// ログインユーザ情報取得
		this.center_cd 			= userBean.getCenterCd();
		this.center_na 			= userBean.getCenterNa();	
		this.nohin_dt 			= SystemControlUtil.getOnlineDate();
		this.torihikisaki_cd 	= userBean.getTorihikisakiCd();
		this.torihikisaki_na 	= userBean.getTorihikisakiNa();
		this.tenpo_cd 			= userBean.getTenpoCd();
		this.tenpo_na 			= userBean.getTenpoNa();
		this.bunrui1_cd 		= userBean.getBunrui1Cd();
		this.bunrui1_na 		= userBean.getBunrui1Na();
		this.syori_jokyo 		= SyoriJokyoDictionary.MIKAKUTEI;
		this.hyoji_kb	 		= HyojiKbDictionary.SYOHIN;
		this.list_target 		= ListTarget.ALL;
		this.torihikisakiCdLen	= Integer.parseInt(SystemControlUtil.getTorihikisakiCdLength());
		this.tenpoCdLen	= Integer.parseInt(SystemControlUtil.getTenpoCdLength());
		this.bunrui1CdLen	= Integer.parseInt(SystemControlUtil.getBunrui1CdLength());
		this.bunrui2CdLen	= Integer.parseInt(SystemControlUtil.getBunrui2CdLength());
		this.bunrui5CdLen	= Integer.parseInt(SystemControlUtil.getBunrui5CdLength());
	}

	/**
	 * @return
	 */
	public String toStringCondition() {
		StringBuffer sb = new StringBuffer();
		
		if(this.center_cd != null && this.center_cd.length() > 0){
			sb.append(", センタコード : "		+ this.center_cd    + ", センタ名称 : "		+ this.center_na);	
		}
		if(this.nohin_dt != null && this.nohin_dt.length() > 0){
			sb.append(", 納品日 : "	+ this.nohin_dt );	
		}
		if(this.torihikisaki_cd != null && this.torihikisaki_cd.length() > 0){
			sb.append("  取引先コード : "		+ this.torihikisaki_cd + ", 取引先名称 : "+ this.torihikisaki_na );	
		}
		if(this.tenpo_cd != null && this.tenpo_cd.length() > 0){
			sb.append(", 店舗コード : "			+ this.tenpo_cd		+ ", 店舗名称 : "		+ this.tenpo_na);	
		}
		if(this.bunrui1_cd != null && this.bunrui1_cd.length() > 0){
			sb.append(", 分類1コード : "		+ this.bunrui1_cd + ", 分類1名称 : "		+ this.bunrui1_na);	
		}
		if(this.bunrui2_cd != null && this.bunrui2_cd.length() > 0){
			sb.append(", 分類2コード : "		+ this.bunrui2_cd + ", 分類2コード : "		+ this.bunrui2_cd);	
		}
		if(this.bunrui5_cd != null && this.bunrui5_cd.length() > 0){
			sb.append(", 分類5コード : "		+ this.bunrui5_cd  + ", 分類5名称 : "		+ this.bunrui5_na);	
		}
		if(this.buturyu_kb != null && this.buturyu_kb.length() > 0){
			sb.append(", 物流区分 : " 			+ this.buturyu_kb  + ", 物流区分名称 : "	+ this.buturyu_nm);	
		}
		if(this.bin_kb != null && this.bin_kb.length() > 0){
			sb.append(", 便区分 : " 			+ this.bin_kb );	
		}		
		sb.append(", 処理状況 : "	+ this.syori_jokyo.toString());		
		sb.append(", 表示形式 : "	+ this.hyoji_kb.toString());

		return sb.toString(); 
	}

	/**
	 * center_cd
	 */
	public String getCenterCd() {
		return center_cd;
	}
	public void setCenterCd(String string) {
		if (string != null && string.trim().length() > 0) {
			center_cd = StringUtility.adjustStringWithZero(string.trim(), 4);
		} else {
			center_cd = string;
		}
	}
	
	/**
	 * center_na
	 */
	public String getCenterNa() {
		return center_na;
	}
	public void setCenterNa(String string) {
		center_na = string;
	}

	/**
	 * nohin_dt
	 */
	public String getNohinDt() {
		return nohin_dt;
	}
	public void setNohinDt(String string) {
		nohin_dt = string;
	}

	/**
	 * torihikisaki_cd
	 */
	public String getTorihikisakiCd() {
		return torihikisaki_cd;
	}
	public void setTorihikisakiCd(String string) {
		if (string != null && string.trim().length() > 0) {
//			torihikisaki_cd = StringUtility.adjustStringWithZero(string.trim(), 6);
			torihikisaki_cd = StringUtility.adjustStringWithZero(string.trim(), this.torihikisakiCdLen);
		} else {
			torihikisaki_cd = string;
		}		
	}
	
	/**
	 * torihikisaki_na
	 */
	public String getTorihikisakiNa() {
		return torihikisaki_na;
	}
	public void setTorihikisakiNa(String string) {
		torihikisaki_na = string;
	}

	/**
	 * tenpo_cd
	 */
	public String getTenpoCd() {
		return tenpo_cd;
	}
	public void setTenpoCd(String string) {
		if (string != null && string.trim().length() > 0) {
//			tenpo_cd = StringUtility.adjustStringWithZero(string.trim(), 4);
			tenpo_cd = StringUtility.adjustStringWithZero(string.trim(), this.tenpoCdLen);
		} else {
			tenpo_cd = string;
		}
	}
	
	/**
	 * tenpo_na
	 */
	public String getTenpoNa() {
		return tenpo_na;
	}
	public void setTenpoNa(String string) {
		tenpo_na = string;
	}	
	
	/**
	 * bunrui1_cd
	 */
	public String getBunrui1Cd() {
		return bunrui1_cd;
	}
	public void setBunrui1Cd(String string) {
		if (string != null && string.trim().length() > 0) {
//			bunrui1_cd = StringUtility.adjustStringWithZero(string.trim(), 2);
			bunrui1_cd = StringUtility.adjustStringWithZero(string.trim(), this.bunrui1CdLen);
		} else {
			bunrui1_cd = string;
		}
	}
	
	/**
	 * bunrui1_na
	 */
	public String getBunrui1Na() {
		return bunrui1_na;
	}
	public void setBunrui1Na(String string) {
		bunrui1_na = string;
	}

	/**
	 * bunrui2_cd
	 */
	public String getBunrui2Cd() {
		return bunrui2_cd;
	}
	public void setBunrui2Cd(String string) {
		if (string != null && string.trim().length() > 0) {
//			bunrui2_cd = StringUtility.adjustStringWithZero(string.trim(), 3);
			bunrui2_cd = StringUtility.adjustStringWithZero(string.trim(), this.bunrui2CdLen);
		} else {
			bunrui2_cd = string;
		}
	}

	/**
	 * bunrui2_na
	 */	
	public String getBunrui2Na() {
		return bunrui2_na;
	}
	public void setBunrui2Na(String string) {
		bunrui2_na = string;
	}

	/**
	 * bunrui5_cd
	 */
	public String getBunrui5Cd() {
		return bunrui5_cd;
	}
	public void setBunrui5Cd(String string) {
		if (string != null && string.trim().length() > 0) {
			bunrui5_cd = StringUtility.adjustStringWithZero(string.trim(), this.bunrui5CdLen);
		} else {
			bunrui5_cd = string;
		}
	}
	
	/**
	 * bunrui5_na
	 */
	public String getBunrui5Na() {
		return bunrui5_na;
	}
	public void setBunrui5Na(String string) {
		bunrui5_na = string;
	}
	
	/**
	* buturyu_kb
	*/	
	public String getButuryuKb() {
		return buturyu_kb;
	}	
	public void setButuryuKb(String string) {
		buturyu_kb = string;
	}

	/**
	* buturyu_nm
	*/
	public String getButuryuNm() {
		return buturyu_nm;
	}	
	public void setButuryuNm(String string) {
		buturyu_nm = string;
	}
	
	/**
	* bin_kb
	*/	
	public String getBinKb() {
		return bin_kb;
	}	
	public void setBinKb(String string) {
		bin_kb = string;
	}
	
	/**
	 * syori_jokyo
	 */
	public SyoriJokyoDictionary getSyoriJokyo() {
		return syori_jokyo;
	}
	public void setSyoriJokyo(SyoriJokyoDictionary dictionary) {
		syori_jokyo = dictionary;
	}	
	
	/**
	 * hyoji_kb
	 */
	public HyojiKbDictionary getHyojiKb() {
		return hyoji_kb;
	}
	public void setHyojiKb(HyojiKbDictionary dictionary) {
		hyoji_kb = dictionary;
	}	
	
	/**
	 * list_target
	 */
	public ListTarget getListTarget() {
		return list_target;
	}
	public void setListTarget(ListTarget target) {
		list_target = target;
	}
	
	/**
	 * retrun_url_id
	 */
	public String getReturnUrlId() {
		return retrun_url_id;
	}
	public void setReturnUrlId(String string) {
			retrun_url_id = string;
	}	

	/**
	 * 
	 */
	public boolean isSyohinSearch() {
		if (hyoji_kb.equals(HyojiKbDictionary.SYOHIN)) {
			return true;
		} else {
			return false;
		}
	}
	/**
	 * 
	 */
	public boolean isDenpyoSearch() {
		if (hyoji_kb.equals(HyojiKbDictionary.DENPYO)) {
			return true;
		} else {
			return false;
		}
	}

}
